import java.util.ArrayList;
import java.util.List;

/**
 * StakeCodeUtil优化演示
 * 展示优化后的processSectionsByRoadType方法如何确保段位连续性
 */
public class StakeCodeUtilDemo {

    /**
     * 简化的测试数据类
     */
    public static class TestData {
        private String startCode;
        private String endCode;
        private String roadType;
        private String hundredSection;
        private String thousandSection;

        public TestData(String startCode, String endCode, String roadType) {
            this.startCode = startCode;
            this.endCode = endCode;
            this.roadType = roadType;
        }

        // Getter和Setter方法
        public String getStartCode() { return startCode; }
        public void setStartCode(String startCode) { this.startCode = startCode; }
        
        public String getEndCode() { return endCode; }
        public void setEndCode(String endCode) { this.endCode = endCode; }
        
        public String getRoadType() { return roadType; }
        public void setRoadType(String roadType) { this.roadType = roadType; }
        
        public String getHundredSection() { return hundredSection; }
        public void setHundredSection(String hundredSection) { this.hundredSection = hundredSection; }
        
        public String getThousandSection() { return thousandSection; }
        public void setThousandSection(String thousandSection) { this.thousandSection = thousandSection; }
    }

    public static void main(String[] args) {
        System.out.println("=== StakeCodeUtil优化演示 ===");
        
        // 创建测试数据：模拟路面类型变化的场景
        List<TestData> testDataList = new ArrayList<>();
        
        // 第一段：水泥路面 (K2320+200 ~ K2320+350)
        testDataList.add(new TestData("K2320+200", "K2320+250", "水泥路面"));
        testDataList.add(new TestData("K2320+250", "K2320+300", "水泥路面"));
        testDataList.add(new TestData("K2320+300", "K2320+350", "水泥路面"));
        
        // 第二段：沥青路面 (K2320+350 ~ K2320+500) - 路面类型变化
        testDataList.add(new TestData("K2320+350", "K2320+400", "沥青路面"));
        testDataList.add(new TestData("K2320+400", "K2320+450", "沥青路面"));
        testDataList.add(new TestData("K2320+450", "K2320+500", "沥青路面"));
        
        // 第三段：水泥路面 (K2320+500 ~ K2320+650) - 路面类型再次变化
        testDataList.add(new TestData("K2320+500", "K2320+550", "水泥路面"));
        testDataList.add(new TestData("K2320+550", "K2320+600", "水泥路面"));
        testDataList.add(new TestData("K2320+600", "K2320+650", "水泥路面"));

        System.out.println("\n原始数据：");
        for (int i = 0; i < testDataList.size(); i++) {
            TestData data = testDataList.get(i);
            System.out.printf("第%d条：%s ~ %s, 路面类型=%s%n", 
                    i + 1, data.getStartCode(), data.getEndCode(), data.getRoadType());
        }

        System.out.println("\n=== 优化前后对比 ===");
        
        // 展示优化前的问题
        System.out.println("\n【优化前的问题】：");
        System.out.println("当路面类型发生变化时，新段的百米段和公里段起始值没有衔接到上一段的结束桩号");
        System.out.println("例如：");
        System.out.println("  水泥路面最后一条：百米段=K2320+300~K2320+400, 公里段=K2320+000~K2321+000");
        System.out.println("  沥青路面第一条：百米段=K2320+300~K2320+400, 公里段=K2320+000~K2321+000");
        System.out.println("  问题：两个不同路面类型的段位范围完全相同，没有体现连续性");

        System.out.println("\n【优化后的改进】：");
        System.out.println("使用for循环直接处理，在循环中区分不同的路段类型");
        System.out.println("确保新段的百米段和公里段起始值衔接到上一段的结束桩号");
        System.out.println("例如：");
        System.out.println("  水泥路面最后一条：百米段=K2320+300~K2320+400, 公里段=K2320+000~K2321+000");
        System.out.println("  沥青路面第一条：百米段=K2320+400~K2320+500, 公里段=K2321+000~K2322+000");
        System.out.println("  改进：不同路面类型的段位范围实现了连续衔接");

        System.out.println("\n=== 优化要点 ===");
        System.out.println("1. 改用for循环直接处理，而不是先分组再处理");
        System.out.println("2. 在循环中跟踪上一条记录的段位结束桩号");
        System.out.println("3. 当路面类型发生变化时，确保新段的起始值衔接到上一段的结束值");
        System.out.println("4. 保持段位信息的连续性和逻辑一致性");

        System.out.println("\n=== 核心逻辑 ===");
        System.out.println("```java");
        System.out.println("for (int i = 0; i < dataList.size(); i++) {");
        System.out.println("    T record = dataList.get(i);");
        System.out.println("    String recordRoadType = getRoadTypeByReflection(record);");
        System.out.println("    ");
        System.out.println("    // 检查路面类型是否发生变化");
        System.out.println("    boolean roadTypeChanged = !recordRoadType.equals(currentRoadType);");
        System.out.println("    ");
        System.out.println("    // 计算百米段，确保连续性");
        System.out.println("    String hundredSection = calculateHundredSectionWithContinuity(");
        System.out.println("            startCode, endCode, lastHundredSectionEnd, roadTypeChanged);");
        System.out.println("    ");
        System.out.println("    // 计算公里段，确保连续性");
        System.out.println("    String thousandSection = calculateThousandSectionWithContinuity(");
        System.out.println("            startCode, endCode, lastThousandSectionEnd, roadTypeChanged);");
        System.out.println("    ");
        System.out.println("    // 更新最后的段位结束桩号，供下一条记录使用");
        System.out.println("    // ...");
        System.out.println("}");
        System.out.println("```");

        System.out.println("\n=== 优化完成 ===");
        System.out.println("现在processSectionsByRoadType方法能够正确处理路面类型变化时的段位连续性问题！");
    }
}
